<template>
  <el-dialog
    v-model="visible"
    title="ES记录完整数据"
    width="90%"
    :before-close="handleClose">

    <div v-if="record" class="detail-container">
      <div class="es-record-detail">
        <!-- 记录概要 -->
        <div class="record-summary">
          <div class="summary-header">
            <h3>{{ record.title || '无标题' }}</h3>
            <div class="summary-meta">
              <el-tag v-if="record.id" type="info" size="small">ID: {{ record.id }}</el-tag>
              <el-tag v-if="record.content_source" :type="getSourceTagType(record.content_source)" size="small">
                {{ getSourceLabel(record.content_source) }}
              </el-tag>
              <el-tag v-if="record.public_time" type="success" size="small">
                {{ formatDate(record.public_time) }}
              </el-tag>
              <el-tag v-if="record._score" type="warning" size="small">
                评分: {{ record._score.toFixed(2) }}
              </el-tag>
            </div>
          </div>
        </div>



        <!-- 完整ES数据展示 -->
        <div class="full-data-section">
          <div class="section-header">
            <h4><i class="el-icon-data-line"></i> 完整ES数据</h4>
            <el-button size="small" @click="toggleDataView" type="text">
              {{ showRawData ? '显示格式化' : '显示原始JSON' }}
            </el-button>
          </div>

          <!-- 格式化数据展示 -->
          <div v-if="!showRawData" class="formatted-data">
            <div class="data-grid">
              <div v-for="(value, key) in record" :key="key" 
                   class="data-item" 
                   :class="{ 'highlight-field': isImportantField(key) }">
                <div class="field-name">{{ key }}</div>
                <div class="field-value">
                  <!-- URL值 -->
                  <div v-if="key === 'url' && value" class="url-value">
                    <a :href="value" target="_blank" class="url-link">{{ value }}</a>
                  </div>
                  <!-- 数组值 -->
                  <div v-else-if="Array.isArray(value)" class="array-value">
                    <el-tag v-if="value.length === 0" size="small" type="info">空数组</el-tag>
                    <div v-else>
                      <div v-for="(item, index) in value" :key="index" class="array-item">
                        {{ item }}
                      </div>
                    </div>
                  </div>
                  <!-- 长内容 -->
                  <div v-else-if="isLongContent(value)" class="long-content">
                    <div class="content-preview">{{ String(value).substring(0, 200) }}...</div>
                    <el-button size="small" type="text" @click="showFullContent(key, value)">查看完整内容</el-button>
                  </div>
                  <!-- 空值 -->
                  <div v-else-if="value === null || value === undefined" class="null-value">
                    <el-tag size="small" type="info">null</el-tag>
                  </div>
                  <!-- 空字符串 -->
                  <div v-else-if="value === ''" class="empty-value">
                    <el-tag size="small" type="info">空字符串</el-tag>
                  </div>
                  <!-- 普通值 -->
                  <div v-else class="normal-value">{{ value }}</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 原始JSON数据展示 -->
          <div v-else class="raw-data">
            <pre class="json-display">{{ JSON.stringify(record, null, 2) }}</pre>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="copyFullData">
          <el-icon><CopyDocument /></el-icon>
          复制完整数据
        </el-button>

      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, defineProps, defineEmits } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  visible: Boolean,
  record: Object
})

const emit = defineEmits(['close', 'show-full-content'])

const showRawData = ref(false)

// 工具函数
const getSourceTagType = (source) => {
  const types = {
    'files_v2': 'primary',
    'text': 'success', 
    'unknown': 'info'
  };
  return types[source] || 'info';
}

const getSourceLabel = (source) => {
  const labels = {
    'files_v2': '文件内容',
    'text': '文本内容',
    'unknown': '未知来源'
  };
  return labels[source] || source;
}

const formatDate = (dateString) => {
  if (!dateString) return '-';
  return dateString;
}



const isImportantField = (key) => {
  const importantFields = [
    'id', 'title', 'content', 'text', 'dynamic_content', 
    'dynamic_content_highlighted', 'keyword', 'public_time', 
    'url', 'content_source', '_score', 'isAddToQueue'
  ];
  return importantFields.includes(key);
}

const isLongContent = (value) => {
  return typeof value === 'string' && value.length > 200;
}

// 事件处理
const handleClose = () => {
  emit('close')
}

const toggleDataView = () => {
  showRawData.value = !showRawData.value
}

const showFullContent = (fieldName, content) => {
  emit('show-full-content', { fieldName, content })
}

const copyFullData = () => {
  if (!props.record) return;

  const jsonData = JSON.stringify(props.record, null, 2);
  navigator.clipboard.writeText(jsonData).then(() => {
    ElMessage.success('完整ES数据已复制到剪贴板');
  }).catch(() => {
    ElMessage.error('复制失败，请手动复制');
  });
}


</script>

<style scoped>
/* 使用现有的对话框样式 */
</style>