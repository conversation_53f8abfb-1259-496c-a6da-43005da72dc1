/* 详情对话框样式 */
.detail-container {
    max-height: 70vh;
    overflow-y: auto;
}

/* 确保对话框居中显示 */
.el-dialog {
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    margin: 0 !important;
    max-height: 90vh !important;
    overflow: hidden !important;
}

/* 对话框遮罩层确保覆盖整个视口 */
.el-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 2000 !important;
}

/* 对话框内容区域滚动 */
.el-dialog__body {
    max-height: calc(90vh - 120px) !important;
    overflow-y: auto !important;
}

.es-record-detail {
    padding: 25px;
    background: #fafbfc;
    border-radius: 0 0 8px 8px;
    margin-top: 0;
}

.record-summary {
    margin-bottom: 30px;
    padding: 25px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.summary-header h3 {
    margin: 0 0 20px 0;
    font-size: 1.6em;
    line-height: 1.3;
    font-weight: 600;
}

.summary-meta {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.content-display {
    line-height: 1.6;
    color: #212529;
    word-break: break-word;
}

.full-data-section {
    background: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 18px 25px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
}

.section-header h4 {
    margin: 0;
    color: #495057;
    font-size: 1.1em;
    font-weight: 600;
}

.formatted-data {
    padding: 25px;
}

.data-grid {
    display: grid;
    gap: 15px;
}

.data-item {
    display: grid;
    grid-template-columns: 200px 1fr;
    gap: 15px;
    padding: 15px;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    background: #ffffff;
    transition: all 0.2s ease;
}

.data-item:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.data-item.highlight-field {
    background: linear-gradient(135deg, #fff3cd 0%, #fef9e7 100%);
    border-color: #ffeaa7;
    box-shadow: 0 2px 8px rgba(255, 234, 167, 0.3);
}

.field-name {
    font-weight: 600;
    color: #495057;
    word-break: break-word;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
}

.field-value {
    color: #212529;
    word-break: break-word;
}

.array-value {
    background: #e3f2fd;
    padding: 8px;
    border-radius: 4px;
}

.array-item {
    padding: 4px 0;
    border-bottom: 1px solid #bbdefb;
}

.array-item:last-child {
    border-bottom: none;
}

.long-content {
    background: #f1f3f4;
    padding: 10px;
    border-radius: 4px;
    border-left: 3px solid #4285f4;
}

.content-preview {
    margin-bottom: 8px;
    line-height: 1.4;
}

.null-value, .empty-value {
    font-style: italic;
}

.url-value a {
    color: #1976d2;
    text-decoration: none;
    word-break: break-all;
}

.url-value a:hover {
    text-decoration: underline;
}

.raw-data {
    padding: 20px;
    background: #f8f9fa;
}

.json-display {
    background: #2d3748;
    color: #e2e8f0;
    padding: 20px;
    border-radius: 6px;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
    line-height: 1.4;
    overflow-x: auto;
    white-space: pre-wrap;
    word-break: break-word;
}

.full-content-container {
    max-height: 60vh;
    overflow-y: auto;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 6px;
}

.dialog-footer {
    text-align: right;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
    background: #fafbfc;
}

.dialog-footer .el-button {
    margin-left: 10px;
    border-radius: 6px;
    font-weight: 500;
}

.dialog-footer .el-button--primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

/* 自定义对话框标题栏样式 */
.custom-dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px 8px 0 0;
    margin: -20px -20px 0 -20px;
}

.custom-dialog-header .dialog-title h3 {
    margin: 0;
    font-size: 1.4em;
    font-weight: 600;
    color: white;
}

.custom-dialog-header .dialog-actions {
    display: flex;
    gap: 10px;
}

.custom-dialog-header .el-button {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.custom-dialog-header .el-button:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.custom-dialog-header .el-button--primary {
    background: rgba(255, 255, 255, 0.9);
    color: #667eea;
    border: none;
}

.custom-dialog-header .el-button--primary:hover {
    background: white;
    color: #5a6fd8;
}

/* 防止对话框被页面滚动影响 */
.el-dialog__wrapper {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    z-index: 2000 !important;
}

/* 对话框动画优化 */
.el-dialog {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.el-dialog.el-dialog--center {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

/* 确保对话框内容不会超出视口 */
.el-dialog__header {
    padding: 0 !important;
    border-bottom: none !important;
}

.el-dialog__footer {
    padding: 10px 20px 20px 20px !important;
}

/* 滚动条样式优化 */
.detail-container::-webkit-scrollbar,
.el-dialog__body::-webkit-scrollbar {
    width: 6px;
}

.detail-container::-webkit-scrollbar-track,
.el-dialog__body::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.detail-container::-webkit-scrollbar-thumb,
.el-dialog__body::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.detail-container::-webkit-scrollbar-thumb:hover,
.el-dialog__body::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}