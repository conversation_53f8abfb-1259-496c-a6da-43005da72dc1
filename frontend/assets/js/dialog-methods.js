// 对话框相关方法
const createDialogMethods = () => {
    return {
        // 关闭详情对话框
        handleDetailClose() {
            this.detailDialogVisible = false;
            this.currentRecord = null;
        },

        // 切换数据显示方式
        toggleDataView() {
            this.showRawData = !this.showRawData;
        },

        // 显示完整内容
        showFullContent(fieldName, content) {
            this.fullContentTitle = `完整内容 - ${fieldName}`;
            this.fullContentValue = typeof content === 'string' ?
                content.replace(/\n/g, '<br>') :
                JSON.stringify(content, null, 2);
            this.fullContentDialogVisible = true;

            // 确保对话框正确居中显示
            this.$nextTick(() => {
                this.ensureDialogCentered();
            });
        },

        // 关闭完整内容对话框
        handleFullContentClose() {
            this.fullContentDialogVisible = false;
            this.fullContentTitle = '';
            this.fullContentValue = '';
        },

        // 复制完整数据
        copyFullData() {
            if (!this.currentRecord) return;

            const jsonData = JSON.stringify(this.currentRecord, null, 2);
            navigator.clipboard.writeText(jsonData).then(() => {
                ElMessage.success('完整ES数据已复制到剪贴板');
            }).catch(() => {
                ElMessage.error('复制失败，请手动复制');
            });
        },

        // 复制主要内容
        copyMainContent() {
            if (!this.currentRecord) return;

            const mainContent = Utils.getMainContent(this.currentRecord);
            if (mainContent) {
                const textContent = mainContent.replace(/<[^>]*>/g, '');
                navigator.clipboard.writeText(textContent).then(() => {
                    ElMessage.success('主要内容已复制到剪贴板');
                }).catch(() => {
                    ElMessage.error('复制失败，请手动复制');
                });
            } else {
                ElMessage.warning('没有找到主要内容');
            }
        },

        // 复制特定内容
        copySpecificContent() {
            if (this.fullContentValue) {
                const textContent = this.fullContentValue.replace(/<[^>]*>/g, '');
                navigator.clipboard.writeText(textContent).then(() => {
                    ElMessage.success('内容已复制到剪贴板');
                }).catch(() => {
                    ElMessage.error('复制失败，请手动复制');
                });
            }
        }
    };
};