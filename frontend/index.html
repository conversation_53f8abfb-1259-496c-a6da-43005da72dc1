<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库查询系统</title>
    
    <!-- External Libraries -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    
    <!-- Element Plus Styles -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Application Styles -->
    <link rel="stylesheet" href="./assets/css/main.css">
    <link rel="stylesheet" href="./assets/css/forms.css">
    <link rel="stylesheet" href="./assets/css/cards.css">
    <link rel="stylesheet" href="./assets/css/tables.css">
    <link rel="stylesheet" href="./assets/css/states.css">
    <link rel="stylesheet" href="./assets/css/pagination.css">
    <link rel="stylesheet" href="./assets/css/tags.css">
    <link rel="stylesheet" href="./assets/css/dialogs.css">
    <link rel="stylesheet" href="./assets/css/groups.css">
    <link rel="stylesheet" href="./assets/css/element-plus.css">
    <link rel="stylesheet" href="./assets/css/responsive.css">
</head>
<body>
    <div id="app">
        <div class="container">
            <!-- 页面头部 -->
            <div class="header">
                <h1>🔍 数据库查询系统</h1>
                <p>智能化参数查询，美观数据展示</p>
            </div>
            
            <!-- 查询表单 -->
            <div class="query-form">
                <h3 style="margin-bottom: 20px; color: #2c3e50;">
                    <el-icon><Search /></el-icon>
                    查询参数设置
                </h3>
                
                <!-- 第一行：主要搜索条件 -->
                <div class="form-row">
                    <div class="form-item" style="flex: 2;">
                        <el-form-item label="搜索关键词">
                            <el-input
                                v-model="queryParams.searchKeyword"
                                placeholder="请输入搜索关键词，多个关键词用逗号分隔"
                                clearable
                                prefix-icon="Search">
                                <template #append>
                                    <el-tooltip content="支持多关键词搜索，用逗号分隔，如：美国,特朗普" placement="top">
                                        <el-icon><QuestionFilled /></el-icon>
                                    </el-tooltip>
                                </template>
                            </el-input>
                        </el-form-item>
                    </div>

                    <div class="form-item" style="flex: 1.5;">
                        <el-form-item label="ID搜索">
                            <el-input
                                v-model="queryParams.idSearch"
                                placeholder="请输入ID，多个ID用逗号分隔"
                                clearable
                                prefix-icon="Key">
                                <template #append>
                                    <el-tooltip content="支持多ID搜索，用逗号分隔，如：123,456,789" placement="top">
                                        <el-icon><QuestionFilled /></el-icon>
                                    </el-tooltip>
                                </template>
                            </el-input>
                        </el-form-item>
                    </div>

                    <div class="form-item" style="flex: 1;">
                        <el-form-item label="内容来源">
                            <el-select v-model="queryParams.contentSourceFilter" placeholder="选择内容来源" clearable style="width: 100%;">
                                <el-option label="全部来源" :value="null"></el-option>
                                <el-option label="文件内容" value="files_v2"></el-option>
                                <el-option label="文本内容" value="text"></el-option>
                            </el-select>
                        </el-form-item>
                    </div>
                </div>

                <!-- 第二行：排序和分页设置 -->
                <div class="form-row">
                    <div class="form-item" style="flex: 1.5;">
                        <el-form-item label="排序字段">
                            <el-select v-model="queryParams.orderBy" placeholder="选择排序方式" style="width: 100%;">
                                <el-option label="发布时间 (降序)" value="public_time DESC"></el-option>
                                <el-option label="发布时间 (升序)" value="public_time ASC"></el-option>
                                <el-option label="相关性评分 (降序)" value="_score DESC"></el-option>
                                <el-option label="相关性评分 (升序)" value="_score ASC"></el-option>
                            </el-select>
                        </el-form-item>
                    </div>

                    <div class="form-item" style="flex: 1;">
                        <el-form-item label="每页显示">
                            <el-select v-model="queryParams.pageSize" placeholder="选择每页显示数量" @change="handlePageSizeChange" style="width: 100%;">
                                <el-option label="10条" :value="10"></el-option>
                                <el-option label="25条" :value="25"></el-option>
                                <el-option label="50条" :value="50"></el-option>
                                <el-option label="100条" :value="100"></el-option>
                                <el-option label="全部数据" :value="'all'"></el-option>
                            </el-select>
                        </el-form-item>
                    </div>

                    <div class="form-item" style="flex: 1.5;">
                        <el-form-item label="ES索引">
                            <el-input
                                v-model="queryParams.tableName"
                                placeholder="Elasticsearch索引名称"
                                readonly>
                            </el-input>
                        </el-form-item>
                    </div>

                    <div class="form-item" style="flex: 1.5;">
                        <el-form-item label="分组显示">
                            <el-switch
                                v-model="queryParams.enableGrouping"
                                active-text="按关键词分组"
                                inactive-text="传统列表"
                                :active-value="true"
                                :inactive-value="false"
                                style="--el-switch-on-color: #667eea; --el-switch-off-color: #dcdfe6"
                                @change="handleGroupingChange">
                            </el-switch>
                            <div v-if="queryParams.enableGrouping && isGroupedView" style="margin-top: 5px; font-size: 12px; color: #666;">
                                <i class="el-icon-info"></i> 分组模式将自动展示全部数据
                            </div>
                        </el-form-item>
                    </div>
                </div>

                <!-- 第三行：查询按钮 -->
                <div class="form-row" style="justify-content: center;">
                    <div class="form-item" style="flex: 0 0 200px;">
                        <el-button
                            type="primary"
                            @click="executeQuery"
                            :loading="loading"
                            size="large"
                            style="width: 100%; background: linear-gradient(45deg, #667eea, #764ba2); border: none;">
                            <el-icon><Search /></el-icon>
                            {{ loading ? '查询中...' : '执行查询' }}
                        </el-button>
                    </div>
                </div>
            </div>
            
            <!-- 统计卡片 -->
            <div v-if="(queryResults.length > 0 && !isGroupedView) || (isGroupedView && Object.keys(groupedResults).length > 0)" class="stats-cards">
                <div class="stat-card" v-if="!isGroupedView">
                    <div class="stat-number">{{ queryResults.length }}</div>
                    <div class="stat-label">查询结果</div>
                </div>
                <div class="stat-card" v-if="isGroupedView">
                    <div class="stat-number">{{ totalRecords }}</div>
                    <div class="stat-label">总记录数</div>
                </div>
                <div class="stat-card" v-if="isGroupedView">
                    <div class="stat-number">{{ Object.keys(groupedResults).length }}</div>
                    <div class="stat-label">关键词组合</div>
                </div>
                <div class="stat-card" v-if="queryParams.searchKeyword">
                    <div class="stat-number">{{ queryParams.searchKeyword }}</div>
                    <div class="stat-label">搜索关键词</div>
                </div>

                <div class="stat-card" v-if="queryParams.idSearch">
                    <div class="stat-number">{{ queryParams.idSearch }}</div>
                    <div class="stat-label">ID搜索</div>
                </div>

                <div class="stat-card" v-if="queryParams.contentSourceFilter">
                    <div class="stat-number">{{ getContentSourceLabel(queryParams.contentSourceFilter) }}</div>
                    <div class="stat-label">内容来源</div>
                </div>

                <div class="stat-card">
                    <div class="stat-number">{{ formatExecutionTime }}</div>
                    <div class="stat-label">执行时间</div>
                </div>
            </div>
            
            <!-- 查询结果 -->
            <div class="results-section">
                <!-- 加载状态 -->
                <div v-if="loading" class="loading-container">
                    <el-icon class="is-loading" size="50" color="#667eea"><Loading /></el-icon>
                    <p>正在查询数据...</p>
                </div>
                
                <!-- 无数据状态 -->
                <div v-else-if="(!queryResults.length && !isGroupedView && hasSearched) || (isGroupedView && !Object.keys(groupedResults).length && hasSearched)" class="no-data">
                    <el-icon size="80" color="#bdc3c7"><DocumentRemove /></el-icon>
                    <h3>未找到匹配的数据</h3>
                    <p>请尝试调整搜索条件或关键词</p>
                </div>
                
                <!-- 分组视图 -->
                <div v-else-if="isGroupedView && Object.keys(groupedResults).length > 0">
                    <div v-for="(group, groupKey) in groupedResults" :key="groupKey" class="group-container">
                        <div class="group-header" @click="toggleGroup(groupKey)">
                            <div class="group-title">
                                <span>🔍</span>
                                <div class="keyword-combination">
                                    <span v-for="keyword in group.keywords" :key="keyword" class="combination-tag">
                                        {{ keyword }}
                                    </span>
                                </div>
                            </div>
                            <div style="display: flex; align-items: center; gap: 15px;">
                                <div class="group-count">{{ group.total_count || 0 }} 条记录</div>
                                <div class="source-summary">
                                    <span v-if="group.sources && group.sources.files_v2 && group.sources.files_v2.count > 0" class="source-tag files-v2">
                                        文件: {{ group.sources.files_v2.count }}
                                    </span>
                                    <span v-if="group.sources && group.sources.text && group.sources.text.count > 0" class="source-tag text">
                                        文本: {{ group.sources.text.count }}
                                    </span>
                                </div>
                                <button class="group-toggle-btn" :class="{ collapsed: !groupExpandedState[groupKey] }">
                                    ▼
                                </button>
                            </div>
                        </div>
                        
                        <div v-show="groupExpandedState[groupKey]" class="group-content">
                            <!-- 按来源分别显示 -->
                            <template v-for="(sourceData, sourceKey) in (group.sources || {})" :key="sourceKey">
                                <div v-if="sourceData && sourceData.count > 0" class="source-section">
                                <div class="source-header">
                                    <div class="source-title">
                                        <span v-if="sourceKey === 'files_v2'">📄</span>
                                        <span v-else>📝</span>
                                        {{ sourceData.source_name || sourceKey }}
                                        <span class="source-count">({{ sourceData.count || 0 }} 条)</span>
                                    </div>
                                    <button class="source-toggle-btn" @click="toggleSource(groupKey, sourceKey)" :class="{ collapsed: !getSourceExpandedState(groupKey, sourceKey) }">
                                        ▼
                                    </button>
                                </div>

                                <div v-show="getSourceExpandedState(groupKey, sourceKey)" class="source-content">
                                    <el-table
                                        :data="sourceData.records || []"
                                        style="width: 100%"
                                        :header-cell-style="{ background: '#f8f9fa', color: '#2c3e50' }"
                                        stripe>
                                
                                <el-table-column prop="id" label="ID" width="100" align="center">
                                    <template #default="scope">
                                        <el-tag type="info" size="small">{{ scope.row.id }}</el-tag>
                                    </template>
                                </el-table-column>

                                <el-table-column prop="title" label="标题" min-width="300">
                                    <template #default="scope">
                                        <div style="font-weight: 500; color: #2c3e50;" v-html="scope.row.title_highlighted || scope.row.title">
                                        </div>
                                    </template>
                                </el-table-column>

                                <el-table-column prop="content" label="内容预览" min-width="350">
                                    <template #default="scope">
                                        <div class="content-preview"
                                             :title="scope.row.dynamic_content || scope.row.content"
                                             v-html="scope.row.dynamic_content_highlighted || scope.row.content_highlighted || scope.row.dynamic_content || scope.row.content">
                                            <div v-if="scope.row.content_source" class="content-source-tag">
                                                {{ scope.row.content_source === 'files_v2' ? '文件内容' : '文本内容' }}
                                            </div>
                                        </div>
                                    </template>
                                </el-table-column>

                                <el-table-column prop="MonitorDate" label="发布日期" width="170">
                                    <template #default="scope">
                                        <span class="date-badge">{{ scope.row.MonitorDate }}</span>
                                    </template>
                                </el-table-column>

                                <el-table-column prop="content_source" label="内容来源" width="120">
                                    <template #default="scope">
                                        <el-tag v-if="scope.row.content_source"
                                                size="small"
                                                :type="scope.row.content_source === 'files_v2' ? 'primary' : 'info'">
                                            {{ scope.row.content_source === 'files_v2' ? 'files_v2' : 'text' }}
                                        </el-tag>
                                        <span v-else style="color: #bdc3c7;">-</span>
                                    </template>
                                </el-table-column>

                                <el-table-column prop="createdAt" label="创建时间" width="180">
                                    <template #default="scope">
                                        <el-icon><Clock /></el-icon>
                                        {{ scope.row.createdAt }}
                                    </template>
                                </el-table-column>

                                <el-table-column label="匹配关键词" width="150">
                                    <template #default="scope">
                                        <div class="keyword-combination">
                                            <span v-for="keyword in scope.row.matched_keywords" :key="keyword" class="combination-tag" style="font-size: 0.75em;">
                                                {{ keyword }}
                                            </span>
                                        </div>
                                    </template>
                                </el-table-column>

                                <el-table-column label="操作" width="150" align="center">
                                    <template #default="scope">
                                        <el-button
                                            type="primary"
                                            size="small"
                                            @click="viewRecord(scope.row)"
                                            style="margin-right: 5px;">
                                            <el-icon><View /></el-icon>
                                            查看详情
                                        </el-button>
                                        <el-button
                                            v-if="scope.row.url"
                                            type="success"
                                            size="small"
                                            @click="openUrl(scope.row.url)"
                                            link>
                                            <el-icon><Link /></el-icon>
                                            原文
                                        </el-button>
                                    </template>
                                </el-table-column>
                                    </el-table>
                                </div>
                                </div>
                            </template>
                        </div>
                    </div>

                    <!-- 分组视图分页控件 -->
                    <div class="pagination-container" v-if="pagination.total_count > 0 && queryParams.pageSize !== 'all'">
                        <el-pagination
                            v-model:current-page="pagination.current_page"
                            :current-page="pagination.current_page"
                            :page-size="pagination.page_size === 'all' ? 100 : pagination.page_size"
                            :page-sizes="[10, 25, 50, 100]"
                            :total="pagination.total_count"
                            layout="total, sizes, prev, pager, next, jumper"
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                            background>
                        </el-pagination>
                    </div>

                    <!-- 分组模式全部数据提示 -->
                    <div class="pagination-container" v-if="Object.keys(groupedResults).length > 0">
                        <div style="color: #666; font-size: 14px; text-align: center;">
                            <i class="el-icon-info"></i>
                            分组模式：已展示全部数据，共 {{ totalRecords }} 条记录
                            <span v-if="totalRecords >= 5000" style="color: #e6a23c; margin-left: 10px;">
                                (为保证性能，最多显示前5000条)
                            </span>
                        </div>
                    </div>
                </div>

                <!-- 传统列表视图 -->
                <div v-else-if="queryResults.length > 0" class="table-container">
                    <el-table 
                        :data="queryResults" 
                        style="width: 100%"
                        :header-cell-style="{ background: '#f8f9fa', color: '#2c3e50' }"
                        stripe>
                        
                        <el-table-column prop="id" label="ID" width="100" align="center">
                            <template #default="scope">
                                <el-tag type="info" size="small">{{ scope.row.id }}</el-tag>
                            </template>
                        </el-table-column>

                        <el-table-column prop="title" label="标题" min-width="320">
                            <template #default="scope">
                                <div style="font-weight: 500; color: #2c3e50;" v-html="scope.row.title_highlighted || scope.row.title">
                                </div>
                            </template>
                        </el-table-column>

                        <el-table-column prop="content" label="内容预览" min-width="420">
                            <template #default="scope">
                                <div class="content-preview"
                                     :title="scope.row.dynamic_content || scope.row.content"
                                     v-html="scope.row.dynamic_content_highlighted || scope.row.content_highlighted || scope.row.dynamic_content || scope.row.content">
                                    <div v-if="scope.row.content_source" class="content-source-tag">
                                        {{ scope.row.content_source === 'files_v2' ? '文件内容' : '文本内容' }}
                                    </div>
                                </div>
                            </template>
                        </el-table-column>

                        <el-table-column prop="MonitorDate" label="发布日期" width="170">
                            <template #default="scope">
                                <span class="date-badge">{{ scope.row.MonitorDate }}</span>
                            </template>
                        </el-table-column>

                        <el-table-column prop="content_source" label="内容来源" width="120">
                            <template #default="scope">
                                <el-tag v-if="scope.row.content_source"
                                        size="small"
                                        :type="scope.row.content_source === 'files_v2' ? 'primary' : 'info'">
                                    {{ scope.row.content_source === 'files_v2' ? 'files_v2' : 'text' }}
                                </el-tag>
                                <span v-else style="color: #bdc3c7;">-</span>
                            </template>
                        </el-table-column>

                        <el-table-column prop="createdAt" label="创建时间" width="180">
                            <template #default="scope">
                                <el-icon><Clock /></el-icon>
                                {{ scope.row.createdAt }}
                            </template>
                        </el-table-column>

                        <el-table-column label="操作" width="150" align="center">
                            <template #default="scope">
                                <el-button
                                    type="primary"
                                    size="small"
                                    @click="viewRecord(scope.row)"
                                    style="margin-right: 5px;">
                                    <el-icon><View /></el-icon>
                                    查看详情
                                </el-button>
                                <el-button
                                    v-if="scope.row.url"
                                    type="success"
                                    size="small"
                                    @click="openUrl(scope.row.url)"
                                    link>
                                    <el-icon><Link /></el-icon>
                                    原文
                                </el-button>
                            </template>
                        </el-table-column>
                    </el-table>

                    <!-- 分页控件 -->
                    <div class="pagination-container" v-if="pagination.total_count > 0 && queryParams.pageSize !== 'all'">
                        <el-pagination
                            v-model:current-page="pagination.current_page"
                            :current-page="pagination.current_page"
                            :page-size="pagination.page_size === 'all' ? 100 : pagination.page_size"
                            :page-sizes="[10, 25, 50, 100]"
                            :total="pagination.total_count"
                            layout="total, sizes, prev, pager, next, jumper"
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                            background>
                        </el-pagination>
                    </div>

                    <!-- 全部数据提示 -->
                    <div class="pagination-container" v-if="queryParams.pageSize === 'all' && queryResults.length > 0">
                        <div style="color: #666; font-size: 14px;">
                            <i class="el-icon-info"></i>
                            显示全部数据：共 {{ totalRecords }} 条记录
                            <span v-if="totalRecords >= 5000" style="color: #e6a23c; margin-left: 10px;">
                                (为保证性能，最多显示前5000条)
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 详情查看对话框 -->
            <el-dialog
                v-model="detailDialogVisible"
                width="90%"
                :before-close="handleDetailClose"
                center
                :modal="true"
                :close-on-click-modal="true"
                :close-on-press-escape="true"
                :show-close="false"
                destroy-on-close
                :append-to-body="true">

                <template #header>
                    <div class="custom-dialog-header">
                        <div class="dialog-title">
                            <h3>ES记录完整数据</h3>
                        </div>
                        <div class="dialog-actions">
                            <el-button type="primary" @click="copyFullData" size="small">
                                <el-icon><CopyDocument /></el-icon>
                                复制完整数据
                            </el-button>
                        </div>
                    </div>
                </template>

                <div v-if="currentRecord" class="detail-container">
                    <!-- ES完整数据展示 -->
                    <div class="es-record-detail">
                        <!-- 记录概要 -->
                        <div class="record-summary">
                            <div class="summary-header">
                                <h3>{{ currentRecord.title || '无标题' }}</h3>
                                <div class="summary-meta">
                                    <el-tag v-if="currentRecord.id" type="info" size="small">ID: {{ currentRecord.id }}</el-tag>
                                    <el-tag v-if="currentRecord.content_source" :type="getSourceTagType(currentRecord.content_source)" size="small">
                                        {{ getSourceLabel(currentRecord.content_source) }}
                                    </el-tag>
                                    <el-tag v-if="currentRecord.public_time" type="success" size="small">
                                        {{ formatDate(currentRecord.public_time) }}
                                    </el-tag>
                                    <el-tag v-if="currentRecord._score" type="warning" size="small">
                                        评分: {{ currentRecord._score.toFixed(2) }}
                                    </el-tag>
                                </div>
                            </div>
                        </div>



                        <!-- 完整ES数据展示 -->
                        <div class="full-data-section">
                            <div class="section-header">
                                <h4><i class="el-icon-data-line"></i> 完整ES数据</h4>
                                <el-button size="small" @click="toggleDataView" type="text">
                                    {{ showRawData ? '显示格式化' : '显示原始JSON' }}
                                </el-button>
                            </div>

                            <!-- 格式化数据展示 -->
                            <div v-if="!showRawData" class="formatted-data">
                                <div class="data-grid">
                                    <div v-for="(value, key) in currentRecord" :key="key" class="data-item" :class="{ 'highlight-field': isImportantField(key) }">
                                        <div class="field-name">{{ key }}</div>
                                        <div class="field-value">
                                            <!-- 处理不同类型的值 -->
                                            <div v-if="key === 'url' && value" class="url-value">
                                                <a :href="value" target="_blank" class="url-link">{{ value }}</a>
                                            </div>
                                            <div v-else-if="Array.isArray(value)" class="array-value">
                                                <el-tag v-if="value.length === 0" size="small" type="info">空数组</el-tag>
                                                <div v-else>
                                                    <div v-for="(item, index) in value" :key="index" class="array-item">
                                                        {{ item }}
                                                    </div>
                                                </div>
                                            </div>
                                            <div v-else-if="isLongContent(value)" class="long-content">
                                                <div class="content-preview">{{ String(value).substring(0, 200) }}...</div>
                                                <el-button size="small" type="text" @click="showFullContent(key, value)">查看完整内容</el-button>
                                            </div>
                                            <div v-else-if="value === null || value === undefined" class="null-value">
                                                <el-tag size="small" type="info">null</el-tag>
                                            </div>
                                            <div v-else-if="value === ''" class="empty-value">
                                                <el-tag size="small" type="info">空字符串</el-tag>
                                            </div>
                                            <div v-else class="normal-value">{{ value }}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 原始JSON数据展示 -->
                            <div v-else class="raw-data">
                                <pre class="json-display">{{ JSON.stringify(currentRecord, null, 2) }}</pre>
                            </div>
                        </div>
                    </div>
                </div>


            </el-dialog>

            <!-- 完整内容查看对话框 -->
            <el-dialog
                v-model="fullContentDialogVisible"
                :title="fullContentTitle"
                width="80%"
                :before-close="handleFullContentClose"
                center
                :modal="true"
                :close-on-click-modal="true"
                :close-on-press-escape="true"
                :show-close="true"
                destroy-on-close
                :append-to-body="true">

                <div class="full-content-container">
                    <div class="content-display" v-html="fullContentValue"></div>
                </div>

                <template #footer>
                    <div class="dialog-footer">
                        <el-button @click="fullContentDialogVisible = false">关闭</el-button>
                        <el-button type="primary" @click="copySpecificContent">
                            <el-icon><CopyDocument /></el-icon>
                            复制此内容
                        </el-button>
                    </div>
                </template>
            </el-dialog>
        </div>
    </div>

    <!-- Application Scripts -->
    <script src="./assets/js/utils.js"></script>
    <script src="./assets/js/data.js"></script>
    <script src="./assets/js/computed.js"></script>
    <script src="./assets/js/methods.js"></script>
    <script src="./assets/js/dialog-methods.js"></script>
    <script src="./assets/js/app.js"></script>
</body>
</html>